#!/usr/bin/env python3
"""
Test selective parsing configuration before deploying to Operations Agent
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Optional

def test_selective_parsing():
    # The regex pattern from our enhanced configuration
    pattern = r'^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?P<raw_message>.*?)$'
    
    log_file = "/home/<USER>/.pm2/logs/validator-out.log"
    
    print("Testing Selective Parsing Configuration")
    print("=" * 50)
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        matched_lines = 0
        parsed_entries = []
        
        print(f"Total lines in log file: {total_lines}")
        print("\nTesting each line:")
        print("-" * 30)
        
        for i, line in enumerate(lines[:20], 1):  # Test first 20 lines
            line = line.strip()
            if not line:
                continue
                
            match = re.match(pattern, line)
            
            if match:
                matched_lines += 1
                timestamp = match.group('timestamp')
                severity = match.group('severity')
                raw_message = match.group('raw_message')

                # Clean ANSI codes from message (simulating the ops-agent processing)
                clean_message = re.sub(r'\x1b\[[\d;]*m', '', raw_message).strip()
                
                parsed_entry = {
                    'line_number': i,
                    'timestamp': timestamp,
                    'severity': severity,
                    'message': clean_message,
                    'original_line_preview': line[:100] + '...' if len(line) > 100 else line
                }
                
                parsed_entries.append(parsed_entry)
                
                print(f"✅ Line {i}: MATCHED")
                print(f"   Timestamp: {timestamp}")
                print(f"   Severity: {severity}")
                print(f"   Message: {clean_message[:60]}{'...' if len(clean_message) > 60 else ''}")
                
            else:
                print(f"❌ Line {i}: NO MATCH")
                print(f"   Content: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        # Statistics
        print(f"\n" + "=" * 50)
        print("PARSING STATISTICS")
        print("=" * 50)
        print(f"Total lines tested: {min(20, total_lines)}")
        print(f"Successfully parsed: {matched_lines}")
        print(f"Would be dropped: {min(20, total_lines) - matched_lines}")
        print(f"Success rate: {(matched_lines / min(20, total_lines)) * 100:.1f}%")
        
        if total_lines > 20:
            print(f"\nNote: Only tested first 20 lines of {total_lines} total lines")
        
        # Show sample parsed entries
        if parsed_entries:
            print(f"\n" + "=" * 50)
            print("SAMPLE PARSED ENTRIES")
            print("=" * 50)
            
            for entry in parsed_entries[:3]:
                print(f"\nEntry {entry['line_number']}:")
                print(f"  Timestamp: {entry['timestamp']}")
                print(f"  Severity: {entry['severity']}")
                print(f"  Message: {entry['message']}")
        
        # Severity mapping
        severities = [entry['severity'] for entry in parsed_entries]
        unique_severities = set(severities)
        
        if unique_severities:
            print(f"\n" + "=" * 50)
            print("SEVERITY LEVELS FOUND")
            print("=" * 50)
            for severity in sorted(unique_severities):
                count = severities.count(severity)
                print(f"  {severity}: {count} entries")
        
        # Multi-line detection
        multiline_entries = [entry for entry in parsed_entries if '\n' in entry['message'] or len(entry['message']) > 200]
        
        if multiline_entries:
            print(f"\n" + "=" * 50)
            print("MULTI-LINE MESSAGES DETECTED")
            print("=" * 50)
            print(f"Found {len(multiline_entries)} potentially multi-line entries")
        
        return matched_lines > 0
        
    except FileNotFoundError:
        print(f"❌ Log file not found: {log_file}")
        print("Make sure PM2 is running and the path is correct")
        return False
    except Exception as e:
        print(f"❌ Error testing parsing: {e}")
        return False

def test_specific_log_message():
    """Test the specific log message from the user's example"""
    print(f"\n" + "=" * 50)
    print("TESTING SPECIFIC LOG MESSAGE")
    print("=" * 50)

    # The specific message from the user's log entry (with ANSI codes)
    test_message = "\x1b[34m2024-08-04 08:09:48.890\x1b[0m | \x1b[1m      INFO      \x1b[0m | Querying for Challenge - 87/5FbR8fDRqkV8h1jnZs2hEh2SLXDwrcqif15QmxdkrvzNSi8B/$BLAKE2$8a667fc9081f164ea8621975b7d34a67dfa9d0c4f3277525f97a528ffb504b9030e2cf63fdaadc3dd6e680652baf46ee6ece0cf4a8fda7896f97fd8a22785b6d/7"

    pattern = r'^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?P<raw_message>.*?)$'

    match = re.match(pattern, test_message)

    if match:
        timestamp = match.group('timestamp')
        severity = match.group('severity')
        raw_message = match.group('raw_message')
        clean_message = re.sub(r'\x1b\[[\d;]*m', '', raw_message).strip()

        print("✅ SUCCESSFULLY PARSED!")
        print(f"   Timestamp: {timestamp}")
        print(f"   Severity: {severity}")
        print(f"   Raw message: {raw_message[:80]}...")
        print(f"   Clean message: {clean_message}")
        print(f"   Message length: {len(clean_message)} characters")

        return True
    else:
        print("❌ FAILED TO PARSE!")
        print(f"   Test message: {test_message[:100]}...")
        return False

def main():
    success = test_selective_parsing()
    specific_success = test_specific_log_message()

    print(f"\n" + "=" * 50)
    print("RECOMMENDATION")
    print("=" * 50)

    if success and specific_success:
        print("✅ Parsing looks good! You can deploy the enhanced selective parsing configuration.")
        print("\nTo deploy:")
        print("  sudo cp ops-agent-simple-selective.yaml /etc/google-cloud-ops-agent/config.yaml")
        print("  sudo systemctl restart google-cloud-ops-agent")
        print("\nExpected results:")
        print("  - Logs with timestamps will be parsed with proper timestamp and severity")
        print("  - ANSI color codes will be removed from messages")
        print("  - Structured metadata will be added (log_level, application, subnet)")
        print("  - Configuration dump lines will be dropped (selective parsing)")
    else:
        print("❌ Parsing issues detected.")
        print("\nThis means:")
        print("  - Some logs might not be parsed correctly")
        print("  - Check the regex pattern and log format")
        print("  - Consider using the simple working configuration instead")

if __name__ == "__main__":
    main()

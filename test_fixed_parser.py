#!/usr/bin/env python3
"""
Test the fixed parser configuration
"""

import re

def test_fixed_parser():
    print("=" * 60)
    print("TESTING FIXED PARSER CONFIGURATION")
    print("=" * 60)
    
    # The corrected regex pattern from our fixed configuration
    pattern = r'^(?:\x1b)?\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?\[[\d;]*m \| (?:\x1b)?\[[\d;]*m\s*(?P<severity>\w+)\s*(?:\x1b)?\[[\d;]*m \| ?(?P<raw_message>.*?)$'
    
    # Test messages
    test_cases = [
        {
            "name": "User's actual log (Google Cloud format)",
            "message": "[34m2024-08-04 08:09:48.890[0m | [1m      INFO      [0m | Querying for Challenge - 87/5FbR8fDRqkV8h1jnZs2hEh2SLXDwrcqif15QmxdkrvzNSi8B/$BLAKE2$8a667fc9081f164ea8621975b7d34a67dfa9d0c4f3277525f97a528ffb504b9030e2cf63fdaadc3dd6e680652baf46ee6ece0cf4a8fda7896f97fd8a22785b6d/7"
        },
        {
            "name": "Sample log (with escape chars)",
            "message": "\x1b[34m2024-06-10 10:46:54.593\x1b[0m | \x1b[1m      INFO      \x1b[0m | Setting up bittensor objects."
        },
        {
            "name": "Error log example",
            "message": "[31m2024-06-10 10:47:15.123[0m | [1m      ERROR     [0m | Connection timeout to validator endpoint"
        },
        {
            "name": "Warning log example", 
            "message": "[33m2024-06-10 10:47:20.456[0m | [1m      WARN      [0m | High memory usage detected: 85%"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test_case['name']} ---")
        message = test_case['message']
        
        match = re.match(pattern, message)
        
        if match:
            timestamp = match.group('timestamp')
            severity = match.group('severity')
            raw_message = match.group('raw_message')
            
            # Simulate the ANSI cleaning (what would happen in the final message)
            clean_message = re.sub(r'(?:\x1b)?\[[\d;]*m', '', raw_message).strip()
            
            print(f"✅ PARSED SUCCESSFULLY")
            print(f"   Timestamp: {timestamp}")
            print(f"   Severity: {severity}")
            print(f"   Raw message: {raw_message[:60]}{'...' if len(raw_message) > 60 else ''}")
            print(f"   Clean message: {clean_message[:60]}{'...' if len(clean_message) > 60 else ''}")
            print(f"   Message length: {len(clean_message)} chars")
            
            success_count += 1
        else:
            print(f"❌ PARSING FAILED")
            print(f"   Message: {message[:80]}{'...' if len(message) > 80 else ''}")
    
    print(f"\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"Tests passed: {success_count}/{len(test_cases)}")
    print(f"Success rate: {(success_count/len(test_cases)*100):.1f}%")
    
    if success_count == len(test_cases):
        print("\n✅ ALL TESTS PASSED!")
        print("The fixed parser should work correctly with your logs.")
        print("\nExpected behavior in Google Cloud Logging:")
        print("  - Timestamp will be properly parsed and indexed")
        print("  - Severity will be extracted (INFO, ERROR, WARN, etc.)")
        print("  - Message will contain the clean log content")
        print("  - Structured fields: log_level, application, subnet")
        print("  - ANSI codes will be handled gracefully")
    else:
        print(f"\n❌ {len(test_cases) - success_count} tests failed.")
        print("The parser may need further adjustments.")
    
    return success_count == len(test_cases)

def simulate_ops_agent_processing():
    """Simulate what the ops-agent would do with the configuration"""
    print(f"\n" + "=" * 60)
    print("SIMULATING OPS-AGENT PROCESSING")
    print("=" * 60)
    
    # User's actual log message
    original_message = "[34m2024-08-04 08:09:48.890[0m | [1m      INFO      [0m | Querying for Challenge - 87/5FbR8fDRqkV8h1jnZs2hEh2SLXDwrcqif15QmxdkrvzNSi8B/$BLAKE2$8a667fc9081f164ea8621975b7d34a67dfa9d0c4f3277525f97a528ffb504b9030e2cf63fdaadc3dd6e680652baf46ee6ece0cf4a8fda7896f97fd8a22785b6d/7"
    
    print(f"Original message: {original_message}")
    
    # Step 1: Parse with regex
    pattern = r'^(?:\x1b)?\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?\[[\d;]*m \| (?:\x1b)?\[[\d;]*m\s*(?P<severity>\w+)\s*(?:\x1b)?\[[\d;]*m \| ?(?P<raw_message>.*?)$'
    match = re.match(pattern, original_message)
    
    if match:
        timestamp = match.group('timestamp')
        severity = match.group('severity')
        raw_message = match.group('raw_message')
        
        print(f"\nStep 1 - Regex parsing:")
        print(f"  timestamp: {timestamp}")
        print(f"  severity: {severity}")
        print(f"  raw_message: {raw_message}")
        
        # Step 2: Final processing (what clean_and_finalize does)
        final_message = raw_message  # In our config, we copy raw_message to message
        
        # Simulate the final log entry structure
        final_log_entry = {
            "timestamp": timestamp,
            "severity": "DEFAULT",  # Google Cloud maps this
            "message": final_message,
            "jsonPayload": {
                "log_level": severity,
                "application": "bittensor-validator", 
                "subnet": "SN27"
            }
        }
        
        print(f"\nStep 2 - Final log entry structure:")
        print(f"  timestamp: {final_log_entry['timestamp']}")
        print(f"  severity: {final_log_entry['severity']}")
        print(f"  message: {final_log_entry['message'][:80]}...")
        print(f"  jsonPayload.log_level: {final_log_entry['jsonPayload']['log_level']}")
        print(f"  jsonPayload.application: {final_log_entry['jsonPayload']['application']}")
        print(f"  jsonPayload.subnet: {final_log_entry['jsonPayload']['subnet']}")
        
        print(f"\n✅ This matches the structure you want!")
        return True
    else:
        print(f"\n❌ Parsing failed!")
        return False

if __name__ == "__main__":
    parser_success = test_fixed_parser()
    simulation_success = simulate_ops_agent_processing()
    
    print(f"\n" + "=" * 60)
    print("FINAL RECOMMENDATION")
    print("=" * 60)
    
    if parser_success and simulation_success:
        print("✅ The fixed configuration should work correctly!")
        print("\nTo deploy:")
        print("  sudo cp ops-agent-simple-selective.yaml /etc/google-cloud-ops-agent/config.yaml")
        print("  sudo systemctl restart google-cloud-ops-agent")
        print("\nThe parser now correctly handles both ANSI formats.")
    else:
        print("❌ There are still issues with the parser configuration.")
        print("Please review the test results above.")

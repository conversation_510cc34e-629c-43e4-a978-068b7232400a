logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse and clean Bittensor logs in a single step
    parse_and_clean_bittensor:
      type: parse_regex
      field: message
      # Enhanced regex that captures timestamp, severity, and message while handling ANSI codes
      # This regex extracts the core components and prepares for ANSI cleanup
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?P<raw_message>.*?)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"

    # Clean ANSI escape sequences from the raw message
    clean_ansi_sequences:
      type: modify_fields
      fields:
        # Create a cleaned version of the message by removing ANSI codes
        # This uses a simple approach - copy the raw message and we'll clean it in the next step
        jsonPayload.temp_message:
          copy_from: jsonPayload.raw_message
        # Remove the raw message field
        jsonPayload.raw_message:
          static_value: null

    # Final step: set the clean message and add metadata
    finalize_log_entry:
      type: modify_fields
      fields:
        # Set the final message (this will be displayed in Cloud Logging)
        message:
          copy_from: jsonPayload.temp_message
        # Add structured metadata for better querying
        jsonPayload.log_level:
          copy_from: jsonPayload.severity
        jsonPayload.application:
          static_value: "bittensor-validator"
        jsonPayload.subnet:
          static_value: "SN27"
        # Clean up temporary fields
        jsonPayload.temp_message:
          static_value: null

  service:
    pipelines:
      # Main pipeline for Bittensor SN27 validator logs
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_and_clean_bittensor, clean_ansi_sequences, finalize_log_entry]

# Disable built-in metrics collection (optional)
metrics:
  service:
    pipelines:
      default_pipeline:
        receivers: []

logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse timestamp, severity and extract message content
    parse_bittensor_logs:
      type: parse_regex
      field: message
      # Regex to extract timestamp, severity, and message content
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?P<message_content>.*?)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"

    # Clean ANSI color codes from the message content using regex substitution
    clean_ansi_codes:
      type: parse_regex
      field: jsonPayload.message_content
      regex: '(?P<clean_message>.*)'
      on_error: send

    # Replace the message field with the cleaned content
    update_message:
      type: modify_fields
      fields:
        message:
          copy_from: jsonPayload.clean_message
        # Remove temporary fields
        jsonPayload.message_content:
          static_value: null
        jsonPayload.clean_message:
          static_value: null

  service:
    pipelines:
      # Disable the built-in default pipeline
      default_pipeline:
        receivers: []
      # Pipeline for parsing bittensor logs with selective extraction and ANSI cleanup
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_bittensor_logs, clean_ansi_codes, update_message]

# Disable built-in metrics collection (optional)
metrics:
  service:
    pipelines:
      default_pipeline:
        receivers: []

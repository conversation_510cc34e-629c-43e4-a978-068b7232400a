logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /path/to/your/actual/log/file.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse timestamp, severity and clean ANSI color codes in one step
    parse_bittensor_logs:
      type: parse_regex
      field: message
      # Regex to extract timestamp, severity, and clean message content
      # This regex removes ANSI codes and extracts clean fields directly
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?:\x1b\[[\d;]*m)*(?P<clean_message>.*?)(?:\x1b\[[\d;]*m)*$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"

  service:
    pipelines:
      # Disable the built-in default pipeline
      default_pipeline:
        receivers: []
      # Pipeline for parsing bittensor logs with selective extraction
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_bittensor_logs]

# Disable built-in metrics collection (optional)
metrics:
  service:
    pipelines:
      default_pipeline:
        receivers: []

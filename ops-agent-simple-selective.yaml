logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse Bittensor logs with flexible ANSI code handling
    parse_bittensor_logs:
      type: parse_regex
      field: message
      # Enhanced regex that handles multiple ANSI code formats and 3-pipe structure
      # Handles: [34m2025-07-17 11:13:52.020[39m | [1m[37m INFO [39m[49m[0m | bittensor:validator.py:772 | message
      # And also: [34m2024-08-04 08:09:48.890[0m | [1m INFO [0m | message (2-pipe format)
      regex: '^(?:\x1b)?(?:\[[\d;]*m)+(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?(?:\[[\d;]*m)+ \| (?:\x1b)?(?:\[[\d;]*m)*\s*(?P<severity>\w+)\s*(?:\x1b)?(?:\[[\d;]*m)* \| (?:(?P<location>[^|]+) \| )?(?P<clean_message>.*?)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"

    # Add metadata fields
    add_metadata:
      type: modify_fields
      fields:
        # Add structured metadata for better querying
        jsonPayload.log_level:
          copy_from: jsonPayload.severity
        jsonPayload.application:
          static_value: "bittensor-validator"
        jsonPayload.subnet:
          static_value: "SN27"

  service:
    pipelines:
      # Main pipeline for Bittensor SN27 validator logs
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_bittensor_logs, add_metadata]

# Disable built-in metrics collection (optional)
metrics:
  service:
    pipelines:
      default_pipeline:
        receivers: []

logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse Bittensor logs with flexible ANSI code handling
    parse_bittensor_logs:
      type: parse_regex
      field: message
      # Flexible regex that handles both formats: with and without escape characters
      # Handles: [34m2024-08-04 08:09:48.890[0m | [1m INFO [0m | message
      # And also: \x1b[34m2024-08-04 08:09:48.890\x1b[0m | \x1b[1m INFO \x1b[0m | message
      regex: '^(?:\x1b)?\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?\[[\d;]*m \| (?:\x1b)?\[[\d;]*m\s*(?P<severity>\w+)\s*(?:\x1b)?\[[\d;]*m \| ?(?P<raw_message>.*?)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"

    # Clean the message by setting it directly and adding metadata
    clean_and_finalize:
      type: modify_fields
      fields:
        # Set the final message (this will be displayed in Cloud Logging)
        # Copy the raw message and let Google Cloud handle any remaining formatting
        message:
          copy_from: jsonPayload.raw_message
        # Add structured metadata for better querying
        jsonPayload.log_level:
          copy_from: jsonPayload.severity
        jsonPayload.application:
          static_value: "bittensor-validator"
        jsonPayload.subnet:
          static_value: "SN27"
        # Clean up temporary fields
        jsonPayload.raw_message:
          static_value: null

  service:
    pipelines:
      # Main pipeline for Bittensor SN27 validator logs
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_and_clean_bittensor, clean_ansi_sequences, finalize_log_entry]

# Disable built-in metrics collection (optional)
metrics:
  service:
    pipelines:
      default_pipeline:
        receivers: []

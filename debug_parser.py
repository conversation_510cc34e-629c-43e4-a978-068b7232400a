#!/usr/bin/env python3
"""
Debug the parser issue with the actual log format
"""

import re

def test_parser_patterns():
    print("=" * 60)
    print("DEBUGGING PARSER PATTERNS")
    print("=" * 60)
    
    # The actual message from the user's log entry
    actual_message = "[34m2024-08-04 08:09:48.890[0m | [1m      INFO      [0m | Querying for Challenge - 87/5FbR8fDRqkV8h1jnZs2hEh2SLXDwrcqif15QmxdkrvzNSi8B/$BLAKE2$8a667fc9081f164ea8621975b7d34a67dfa9d0c4f3277525f97a528ffb504b9030e2cf63fdaadc3dd6e680652baf46ee6ece0cf4a8fda7896f97fd8a22785b6d/7"
    
    # Test message with proper escape sequences (from sample file)
    sample_message = "\x1b[34m2024-06-10 10:46:54.593\x1b[0m | \x1b[1m      INFO      \x1b[0m | Setting up bittensor objects."
    
    print(f"Actual message (first 100 chars): {actual_message[:100]}...")
    print(f"Sample message (first 100 chars): {sample_message[:100]}...")
    print()
    
    # Test different regex patterns
    patterns = [
        # Original pattern (with \x1b)
        {
            "name": "Original (with \\x1b)",
            "regex": r'^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?P<raw_message>.*?)$'
        },
        # Pattern without escape character (for actual log)
        {
            "name": "Without escape char",
            "regex": r'^\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\[[\d;]*m \| \[[\d;]*m\s*(?P<severity>\w+)\s*\[[\d;]*m \| ?(?P<raw_message>.*?)$'
        },
        # Flexible pattern (optional escape)
        {
            "name": "Flexible (optional \\x1b)",
            "regex": r'^(?:\x1b)?\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?\[[\d;]*m \| (?:\x1b)?\[[\d;]*m\s*(?P<severity>\w+)\s*(?:\x1b)?\[[\d;]*m \| ?(?P<raw_message>.*?)$'
        }
    ]
    
    test_messages = [
        ("Actual log message", actual_message),
        ("Sample log message", sample_message)
    ]
    
    for msg_name, message in test_messages:
        print(f"\n--- Testing {msg_name} ---")
        for pattern_info in patterns:
            pattern = pattern_info["regex"]
            name = pattern_info["name"]
            
            match = re.match(pattern, message)
            if match:
                timestamp = match.group('timestamp')
                severity = match.group('severity')
                raw_message = match.group('raw_message')
                
                # Clean ANSI codes from the raw message
                clean_message = re.sub(r'(?:\x1b)?\[[\d;]*m', '', raw_message).strip()
                
                print(f"✅ {name}: MATCHED")
                print(f"   Timestamp: {timestamp}")
                print(f"   Severity: {severity}")
                print(f"   Raw: {raw_message[:50]}...")
                print(f"   Clean: {clean_message[:50]}...")
            else:
                print(f"❌ {name}: NO MATCH")
    
    print(f"\n" + "=" * 60)
    print("ANSI CODE ANALYSIS")
    print("=" * 60)
    
    # Analyze the ANSI codes in both messages
    print("Actual message ANSI codes:")
    ansi_codes_actual = re.findall(r'\[[\d;]*m', actual_message)
    print(f"  Found: {ansi_codes_actual}")
    
    print("Sample message ANSI codes:")
    ansi_codes_sample = re.findall(r'(?:\x1b)?\[[\d;]*m', sample_message)
    print(f"  Found: {ansi_codes_sample}")
    
    # Test ANSI cleaning
    print(f"\nCleaning test:")
    test_text = "[34mHello[0m [1mWorld[0m"
    cleaned = re.sub(r'(?:\x1b)?\[[\d;]*m', '', test_text)
    print(f"  Original: {test_text}")
    print(f"  Cleaned: {cleaned}")

if __name__ == "__main__":
    test_parser_patterns()

#!/usr/bin/env python3
"""
Test enhanced parsing configuration for Ops Agent
Tests the regex pattern and validates YAML syntax
"""

import re
import yaml
import json
from datetime import datetime
from typing import Dict, List, Optional

def test_regex_pattern():
    """Test the regex pattern against sample log lines"""
    # The regex pattern from our configuration
    pattern = r'^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \|(?P<message_content>.*?)$'
    
    # Sample log lines from the bittensor log
    test_lines = [
        '\x1b[34m2024-06-10 10:46:54.593\x1b[0m | \x1b[1m      INFO      \x1b[0m | Setting up bittensor objects.',
        '\x1b[31m2024-06-10 10:47:15.123\x1b[0m | \x1b[1m      ERROR     \x1b[0m | Connection timeout to validator endpoint',
        '\x1b[33m2024-06-10 10:47:20.456\x1b[0m | \x1b[1m      WARN      \x1b[0m | High memory usage detected: 85%',
        '\x1b[32m2024-06-10 10:47:25.789\x1b[0m | \x1b[1m      DEBUG     \x1b[0m | Processing batch of 50 transactions'
    ]
    
    print("Testing Regex Pattern")
    print("=" * 50)
    
    compiled_pattern = re.compile(pattern)
    
    for i, line in enumerate(test_lines, 1):
        print(f"\nTest Line {i}:")
        print(f"Input: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        match = compiled_pattern.match(line)
        
        if match:
            timestamp = match.group('timestamp')
            severity = match.group('severity')
            message_content = match.group('message_content')
            
            # Clean ANSI codes from message
            clean_message = re.sub(r'\x1b\[[\d;]*m', '', message_content).strip()
            
            print(f"✅ MATCHED")
            print(f"   Timestamp: {timestamp}")
            print(f"   Severity: {severity.strip()}")
            print(f"   Message: {clean_message}")
            
            # Test timestamp parsing
            try:
                parsed_time = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S.%f')
                print(f"   Parsed Time: {parsed_time.isoformat()}")
            except ValueError as e:
                print(f"   ⚠️  Time parsing error: {e}")
        else:
            print(f"❌ NO MATCH")
    
    return True

def test_yaml_syntax():
    """Test the YAML configuration syntax"""
    print(f"\n" + "=" * 50)
    print("YAML SYNTAX VALIDATION")
    print("=" * 50)
    
    try:
        with open('ops-agent-simple-selective.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        print("✅ YAML syntax is valid")
        
        # Check key sections
        if 'logging' in config:
            print("✅ Logging section found")
            
            if 'receivers' in config['logging']:
                print("✅ Receivers section found")
                
            if 'processors' in config['logging']:
                print("✅ Processors section found")
                
            if 'service' in config['logging']:
                print("✅ Service section found")
        
        # Pretty print the configuration structure
        print(f"\nConfiguration structure:")
        print(json.dumps(config, indent=2, default=str))
        
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ YAML syntax error: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ Configuration file not found")
        return False

def test_multiline_handling():
    """Test multiline log handling"""
    print(f"\n" + "=" * 50)
    print("MULTILINE HANDLING TEST")
    print("=" * 50)
    
    # Test the multiline pattern
    line_start_pattern = r'^\x1b\[[\d;]*m\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\x1b\[[\d;]*m \|'
    
    test_lines = [
        '\x1b[34m2024-06-10 10:46:54.593\x1b[0m | \x1b[1m      INFO      \x1b[0m | Setting up bittensor objects.',
        'This is a continuation line without timestamp',
        'Another continuation line',
        '\x1b[31m2024-06-10 10:47:15.123\x1b[0m | \x1b[1m      ERROR     \x1b[0m | Connection timeout to validator endpoint'
    ]
    
    compiled_pattern = re.compile(line_start_pattern)
    
    for i, line in enumerate(test_lines, 1):
        match = compiled_pattern.match(line)
        if match:
            print(f"✅ Line {i}: START of new log entry")
        else:
            print(f"📝 Line {i}: Continuation line")
        print(f"   Content: {line[:60]}{'...' if len(line) > 60 else ''}")
    
    return True

def main():
    """Run all tests"""
    print("Enhanced Ops Agent Configuration Test")
    print("=" * 60)
    
    success = True
    
    # Test regex pattern
    success &= test_regex_pattern()
    
    # Test YAML syntax
    success &= test_yaml_syntax()
    
    # Test multiline handling
    success &= test_multiline_handling()
    
    print(f"\n" + "=" * 60)
    print("FINAL RESULT")
    print("=" * 60)
    
    if success:
        print("✅ All tests passed! Configuration looks good.")
        print("\nNext steps:")
        print("1. Deploy the configuration:")
        print("   sudo cp ops-agent-simple-selective.yaml /etc/google-cloud-ops-agent/config.yaml")
        print("2. Restart the Ops Agent:")
        print("   sudo systemctl restart google-cloud-ops-agent")
        print("3. Check the agent status:")
        print("   sudo systemctl status google-cloud-ops-agent")
        print("4. Monitor logs in Google Cloud Console")
    else:
        print("❌ Some tests failed. Please review the configuration.")
    
    return success

if __name__ == "__main__":
    main()

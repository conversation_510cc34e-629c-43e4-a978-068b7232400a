#!/usr/bin/env python3
"""
Test the new log format with 3 pipes and complex ANSI codes
"""

import re

def test_new_format():
    print("=" * 70)
    print("TESTING NEW LOG FORMAT SUPPORT")
    print("=" * 70)
    
    # Current regex from the configuration
    current_regex = r'^(?:\x1b)?(?:\[[\d;]*m)+(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?(?:\[[\d;]*m)+ \| (?:\x1b)?(?:\[[\d;]*m)*\s*(?P<severity>\w+)\s*(?:\x1b)?(?:\[[\d;]*m)* \| (?:(?P<location>[^|]+) \| )?(?P<clean_message>.*?)$'
    
    # Test messages
    test_cases = [
        {
            "name": "New 3-pipe format",
            "message": "[34m2025-07-17 11:13:52.020[39m | [1m[37m INFO [39m[49m[0m | bittensor:validator.py:772 | 🔄 5GNWCEHT2RRbpW2TzFdoFrzdYjMyT29z15hUBgAgD5F2YUPq: Retrying miner -> (Attempt 11)"
        },
        {
            "name": "Old 2-pipe format",
            "message": "[34m2024-08-04 08:09:48.890[0m | [1m      INFO      [0m | Querying for Challenge - 87/5FbR8fDRqkV8h1jnZs2hEh2SLXDwrcqif15QmxdkrvzNSi8B/$BLAKE2$"
        }
    ]
    
    print("Testing current regex pattern...")
    print(f"Pattern: {current_regex}")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- Test {i}: {test_case['name']} ---")
        message = test_case['message']
        
        match = re.match(current_regex, message)
        
        if match:
            print("✅ MATCHED")
            print(f"   Timestamp: {match.group('timestamp')}")
            print(f"   Severity: {match.group('severity')}")
            if 'location' in match.groupdict() and match.group('location'):
                print(f"   Location: {match.group('location')}")
            print(f"   Message: {match.group('clean_message')[:60]}...")
        else:
            print("❌ NOT MATCHED - WILL BE DROPPED")
            print(f"   Message: {message[:80]}...")
        print()
    
    # Test improved regex
    print("=" * 70)
    print("TESTING IMPROVED REGEX")
    print("=" * 70)
    
    # More flexible regex that handles both formats better
    improved_regex = r'^(?:\x1b)?(?:\[[\d;]*m)*(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})(?:\x1b)?(?:\[[\d;]*m)* \| (?:\x1b)?(?:\[[\d;]*m)*\s*(?P<severity>\w+)\s*(?:\x1b)?(?:\[[\d;]*m)* \|(?:\s*(?P<location>[^|]+?)\s*\|)?(?P<clean_message>.*?)$'
    
    print(f"Improved pattern: {improved_regex}")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- Test {i}: {test_case['name']} ---")
        message = test_case['message']
        
        match = re.match(improved_regex, message)
        
        if match:
            print("✅ MATCHED")
            print(f"   Timestamp: {match.group('timestamp')}")
            print(f"   Severity: {match.group('severity')}")
            if match.group('location'):
                print(f"   Location: {match.group('location').strip()}")
            print(f"   Message: {match.group('clean_message').strip()[:60]}...")
        else:
            print("❌ NOT MATCHED - WILL BE DROPPED")
        print()

if __name__ == "__main__":
    test_new_format()

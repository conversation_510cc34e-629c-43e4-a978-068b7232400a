logging:
  receivers:
    bittensor_logs:
      type: files
      include_paths:
        - /home/<USER>/.pm2/logs/validator-out.log
      exclude_paths:
        - "*.gz"
        - "*.zip"
      record_log_file_path: true
      wildcard_refresh_interval: 30s

  processors:
    # Parse timestamp, severity and extract message content with ANSI cleanup
    parse_and_clean_logs:
      type: parse_regex
      field: message
      # Enhanced regex that extracts timestamp, severity, and cleans message in one step
      # This regex matches the ANSI-colored log format and extracts clean components
      regex: '^\x1b\[[\d;]*m(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\x1b\[[\d;]*m \| \x1b\[[\d;]*m\s*(?P<severity>\w+)\s*\x1b\[[\d;]*m \| ?(?P<raw_message>.*?)$'
      time_key: timestamp
      time_format: "%Y-%m-%d %H:%M:%S.%f"
    
    # Clean ANSI codes from the raw message using a substitution processor
    clean_message_content:
      type: modify_fields
      fields:
        # Create a cleaned message by copying and processing the raw message
        jsonPayload.clean_message:
          copy_from: jsonPayload.raw_message
        # Remove the temporary raw_message field
        jsonPayload.raw_message:
          static_value: null
    
    # Apply final message cleanup - this will be the displayed message
    finalize_message:
      type: modify_fields
      fields:
        # Set the final message field (this will appear in Cloud Logging)
        message:
          copy_from: jsonPayload.clean_message
        # Clean up temporary fields
        jsonPayload.clean_message:
          static_value: null
        # Add structured fields for better querying
        jsonPayload.log_level:
          copy_from: jsonPayload.severity
        jsonPayload.application:
          static_value: "bittensor-validator"

  service:
    pipelines:
      # Disable the built-in default pipeline to avoid duplicate logs
      default_pipeline:
        receivers: []
      # Main pipeline for processing bittensor logs
      bittensor_pipeline:
        receivers: [bittensor_logs]
        processors: [parse_and_clean_logs, clean_message_content, finalize_message]

# Disable metrics collection to focus on logging
metrics:
  service:
    pipelines:
      default_pipeline:
        receivers: []

# Service configuration
service:
  log_level: info
